{"version": "2.0.0", "tasks": [{"type": "cppbuild", "label": "C/C++: g+ build active file", "command": "g++", "args": ["-g", "-std=c++17", "-I${workspaceFolder}/include", "-L${workspaceFolder}/lib", "${workspaceFolder}/src/main.cpp", "${workspaceFolder}/src/glad.c", "-lglfw3", "-o", "${workspaceFolder}/opengl_bin"], "options": {"cwd": "${workspaceFolder}"}, "problemMatcher": ["$gcc"], "group": {"kind": "build", "isDefault": true}, "detail": "compiler: g++"}]}